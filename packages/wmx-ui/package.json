{"name": "wmx-ui", "version": "0.0.1", "main": "./index.tsx", "types": "./index.tsx", "license": "MIT", "scripts": {"lint": "eslint \"**/*.ts*\"", "generate:component": "turbo gen react-component"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/react-syntax-highlighter": "^15.5.7", "eslint": "^7.32.0", "eslint-config-custom": "*", "react": "^18.2.0", "tailwind-config": "*", "tsconfig": "*", "typescript": "^5.1.6"}, "publishConfig": {"access": "public"}, "dependencies": {"@heroicons/react": "^2.0.18", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.3", "@radix-ui/react-navigation-menu": "^1.1.2", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "class-variance-authority": "^0.6.1", "clipboard": "^2.0.11", "clsx": "^1.2.1", "katex": "^0.16.8", "lucide-react": "^0.260.0", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "rehype-katex": "^6.0.3", "rehype-raw": "^6.1.1", "remark-directive": "^2.0.1", "remark-emoji": "^3.1.2", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6", "unist-util-visit": "^5.0.0"}}