{"name": "wmx-advisor-desktop", "private": true, "description": "", "version": "0.0.1", "license": "MIT", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "dotenv -- turbo run build", "start": "dotenv -- turbo run start", "dev": "dotenv -- turbo run dev --parallel", "graph": "turbo run build --graph", "dev-storybook": "turbo run dev-storybook", "build-storybook": "turbo run build-storybook", "test-storybook": "test-storybook", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean && rimraf node_modules", "prepare": "husky install", "format": "prettier --write \"**/README.md\" \"**/src/**/*.{js,jsx,ts,tsx,json}\"", "gen-code": "turbo gen"}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": "prettier --write"}, "engines": {"node": ">=16.0.0"}, "devDependencies": {"@tailwindcss/line-clamp": "^0.4.4", "@turbo/gen": "^1.9.7", "@types/react-speech-recognition": "^3.9.5", "eslint": "^7.32.0", "eslint-config-custom": "*", "husky": "^8.0.3", "lint-staged": "^13.1.0", "prettier": "^2.8.3", "rimraf": "^4.1.1", "swiper": "^11.1.4", "turbo": "^1.10.9"}, "packageManager": "npm@9.5.1", "dependencies": {"@heroicons/react": "^2.1.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@shadcn/ui": "^0.0.4", "@svgr/webpack": "^8.0.1", "@tanstack/react-table": "^8.17.3", "apexcharts": "^3.42.0", "chart.js": "^4.4.3", "classnames": "^2.3.2", "clipboard": "^2.0.11", "dayjs": "^1.11.11", "embla-carousel-react": "^8.3.0", "flowbite": "^2.4.1", "html-to-text": "^9.0.5", "jspdf": "^2.5.1", "lucide-react": "^0.441.0", "pg": "^8.12.0", "react-apexcharts": "^1.4.1", "react-circular-progressbar": "^2.1.0", "react-icons": "^5.2.1", "react-speech-recognition": "^3.10.0"}}