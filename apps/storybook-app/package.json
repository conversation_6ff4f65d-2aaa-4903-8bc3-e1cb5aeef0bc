{"name": "storybook-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev-storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test-storybook": "test-storybook"}, "dependencies": {"@storybook/addon-a11y": "^7.1.1", "autoprefixer": "^10.4.14", "postcss": "^8.4.27", "react": "^18.2.0", "react-dom": "^18.2.0", "wmx-ui": "*"}, "devDependencies": {"@storybook/addon-essentials": "^7.1.1", "@storybook/addon-interactions": "^7.1.1", "@storybook/addon-links": "^7.1.1", "@storybook/addon-styling": "^1.3.4", "@storybook/blocks": "^7.1.1", "@storybook/jest": "^0.1.0", "@storybook/manager-api": "^7.1.1", "@storybook/react": "^7.1.1", "@storybook/react-vite": "^7.1.1", "@storybook/test-runner": "^0.11.0", "@storybook/testing-library": "^0.2.0", "@storybook/theming": "^7.1.1", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react-swc": "^3.3.2", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "eslint-plugin-storybook": "^0.6.13", "prop-types": "^15.8.1", "storybook": "^7.1.1", "tailwind-config": "*", "typescript": "^5.0.2", "vite": "^4.4.0"}}