import { useCallback, useEffect, useState } from "react";
import { nanoid } from "nanoid";

import { fetchJson } from "@/services/fetcher";
import {
  AUTHORIZATION_TOKEN,
  CHAT_BASE_URL,
  CONVERSATION_CHAT_API
} from "@/services/api.constants";
import { useRouter } from "next/router";
import { useAuthService } from "@/services/auth/useAuth";

const uniqId = nanoid();

type MessageItemType = {
  id: string;
  role: "Human" | "Bot";
  message: string;
  promptContent?: string;
};

type NewChatData =
  | { chatId: string; chatMessage: MessageItemType }
  | { error: boolean; errorObj: Error };

const getInitChatData = async (): Promise<NewChatData> => {
  const data = await fetchJson("/v1/chat/new", {}, CHAT_BASE_URL);
  return data as NewChatData;
};

export const useChat = ({
  initChatMsg,
  chatHistory = []
}: {
  initChatMsg: string;
  chatHistory: MessageItemType[];
}) => {
  const { user: { userid, email } = {} } = useAuthService();

  const [data, setData] = useState<MessageItemType[]>(chatHistory);
  const [chatId, setChatId] = useState<string>("");
  const { query, replace } = useRouter();
  const { id } = query;
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (chatHistory.length) setData(chatHistory);
  }, [chatHistory]);

  const handleNewChat = useCallback(async () => {
    try {
      setIsLoading(true);
      const initChatData: {
        id: string;
        role: "Human" | "Bot";
        message: string;
      } = { id: uniqId, role: "Bot", message: initChatMsg };

      // setChatId(nanoid());
      setData([]);
      //replace("/chat");
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, [initChatMsg]);

  const sendChatMsgJson = useCallback(
    //Initialised file to add file name
    async (promptMsg: string, files: File[] = []) => {
      try {
        const aborter = new AbortController();
        const humanMsgId = nanoid();
        setIsLoading(true);

        let fileNamesText = "";
        if (files.length > 0) {
          const fileNames = files.map((file) => `**${file.name}**`).join(", ");
          fileNamesText = `\n\n${fileNames}`;
        }
        const messageToSend = `${promptMsg}${fileNamesText}`;
        
        setData((p) => [
          ...p,
          {
            id: humanMsgId,
            role: "Human",
            message: messageToSend,
            promptContent: promptMsg
          }
        ]);

        const chatData = await fetchJson(
          CONVERSATION_CHAT_API,
          {
            method: "POST",
            signal: aborter?.signal,
            body: JSON.stringify({
              user: {
                user_id: userid,
                email: email
              },
              prompt_type: "system",
              source_info_response: "N",
              context: {
                required: "Y",
                knowledge_base: "wmxdocs",
                k_value: 5,
                multi_query: "disabled"
              },
              ...(id ? { session_id: id } : null),
              message: {
                content: promptMsg,
                role: "Human"
              }
            }),
            headers: {
              Authorization: `Basic ${AUTHORIZATION_TOKEN}`,
              "Content-Type": "application/json"
            }
          },
          CHAT_BASE_URL
        );

        if (chatData) {
          const botMessage = chatData?.messages[0];
          if (!id) {
            //replace({ pathname: "/chat", query: { id: chatData.session_id } });
          }

          setData((p) => [
            ...p,
            {
              id: botMessage.message_id,
              role: botMessage.role,
              message: botMessage.content,
              promptContent: botMessage.content
            }
          ]);
        }
      } catch (err) {
        setData((p) => [
          ...p,
          {
            id: "error",
            role: "Bot",
            message: "Something went wrong",
            promptContent: "No Content"
          }
        ]);
      } finally {
        setIsLoading(false);
      }
    },
    [email, id, userid, replace]
  );
  return { handleNewChat, sendChatMsgJson, data, isLoading };
};
