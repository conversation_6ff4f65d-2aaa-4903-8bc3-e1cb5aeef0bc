// Define ChatSettingJsonParser function
export const ChatSettingJsonToStateParser = (data) => {
  const defaults = {
    randomness: 0.4,
    tokenLimit: 400,
    topK: 20,
    topP: 0.9,
    defaultTone: "formal",
    stopSequences: ["New Line", "Sentence"],
    provider: "Microsoft Azure",
    model: "gpt-3.5-turbo",
    retriever: "Redis",
    dataSource: "researchDoc", // Default value that will be updated
    searchReRank: "Cohere",
    multiQuery: false,
    guardRail: true,
    promptType: "wealth",
    prompt: ""
  };

  // Find the retriever with 'default' set to true
  const defaultRetriever = Object.values(data.retrievers).find(
    (retriever) => retriever["default"] === true
  );

  return {
    randomness:
      parseFloat(data.foundation_model?.temperature) || defaults.randomness,
    tokenLimit: data.foundation_model?.token_limit || defaults.tokenLimit,
    topK: data.foundation_model?.top_k || defaults.topK,
    topP: parseFloat(data.foundation_model?.top_p) || defaults.topP,
    defaultTone:
      data.conversation_tone?.message_tone.toLowerCase() ||
      defaults.defaultTone,
    stopSequences: data.foundation_model?.stop_sequence
      ? data.foundation_model.stop_sequence.split("\n\n")
      : defaults.stopSequences,
    provider: data.foundation_model?.fm_model_provider || defaults.provider,
    model: data.foundation_model?.fm_model_name || defaults.model,
    retriever: defaultRetriever["retriever_name"] || defaults.retriever,
    dataSource: defaultRetriever
      ? defaultRetriever["index_name"]
      : defaults.dataSource, // Use index_name from the default retriever
    searchReRank:
      data.feature_toggles?.search_rerank?.config_value ||
      defaults.searchReRank,
    multiQuery:
      data.feature_toggles?.multi_query_enabled?.config_value === "true" ||
      defaults.multiQuery,
    guardRail: data.guardrails_provider?.feature_enabled || defaults.guardRail,
    promptType: defaults.promptType,
    prompt: data.system_prompt || defaults.prompt
  };
};

// Define StateToChatSettingJsonParser function
export const StateToChatSettingJsonParser = (state) => {
  // Retrieve existing JSON structure for base
  const existingJson = {
    foundation_model: {
      id: 8, // placeholder id
      fm_model_provider: "Microsoft Azure",
      fm_model_name: "gpt-3.5-turbo",
      fm_model_id: "gpt-35-turbo",
      temperature: "0.2",
      token_limit: 4096,
      stop_sequence: "\n\n",
      top_k: 15,
      top_p: "1.0",
      cloud_region: "us-east-2"
    },
    conversation_tone: {
      id: 10,
      message_tone: "Professional",
      tone_text:
        "Maintain a professional and informative tone, providing expert advice and recommendations."
    },
    retrievers: {
      AzureAISearch: {
        id: 6,
        retriever_name: "AzureAISearch",
        k_value: 5,
        connection_url: null,
        index_name: "pschatwestidx",
        region_name: "semantic_hybrid",
        default: false
      },
      Redis: {
        id: 1,
        retriever_name: "Redis",
        k_value: 10,
        connection_url: "",
        index_name: "wmxdocs",
        region_name: "wmxdocs",
        default: true
      }
    },
    default_retriever: "Redis",
    system_prompt:
      "You are an experienced assistant for question-answering tasks with respect to providing assistance to financial advisors in a wealth management firm. If you don't know the answer, just say that you don't know. \n\nBe descriptive in your response and highlight key points in bullets.\nAnswer the question based only on the following context:\n\n{prompt_tone}\n\nContext:\n{context}\n\nQuestion: {question}\n\nAnswer:\n\n",
    prompt_id: 6,
    feature_toggles: {
      user_intent_enabled: {
        config_id: 14,
        config_mapping_id: 0,
        config_type: "user_intent_enabled",
        config_value: "true",
        default_value: "true",
        feature_toggle: true
      },
      search_rerank: {
        config_id: 9,
        config_mapping_id: 0,
        config_type: "search_rerank",
        config_value: "Cohere",
        default_value: "Cohere",
        feature_toggle: true
      },
      multi_query_enabled: {
        config_id: 10,
        config_mapping_id: 0,
        config_type: "multi_query_enabled",
        config_value: "true",
        default_value: "true",
        feature_toggle: true
      },
      sources_in_response: {
        config_id: 11,
        config_mapping_id: 0,
        config_type: "sources_in_response",
        config_value: "true",
        default_value: "true",
        feature_toggle: true
      }
    },
    cache_provider: {
      provider_id: 1,
      provider_name: "Redis",
      connection_url: "",
      index_name: "wmx_chat_cache",
      semantic_cache_enabled: true,
      ttl: -1,
      token: "token"
    },
    guardrails_provider: {
      provider_id: 1,
      provider_name: "nemo-guardrails",
      feature_enabled: false
    }
  };

  // Prepare the retrievers object
  const retrievers = {
    ...existingJson.retrievers,
    [state.retriever]: {
      id: existingJson.retrievers[state.retriever]?.id || 1, // default id if not found
      retriever_name: state.retriever,
      k_value: existingJson.retrievers[state.retriever]?.k_value || 10, // default k_value if not found
      connection_url:
        existingJson.retrievers[state.retriever]?.connection_url || "", // default connection_url
      index_name: state.dataSource,
      region_name: existingJson.retrievers[state.retriever]?.region_name || "", // default region_name if not found
      default: true
    }
  };

  return {
    ...existingJson,
    foundation_model: {
      ...existingJson.foundation_model,
      fm_model_provider:
        state.provider || existingJson.foundation_model.fm_model_provider,
      fm_model_name: state.model || existingJson.foundation_model.fm_model_name,
      temperature:
        state.randomness !== undefined
          ? state.randomness.toFixed(1)
          : existingJson.foundation_model.temperature,
      token_limit:
        state.tokenLimit || existingJson.foundation_model.token_limit,
      stop_sequence: state.stopSequences
        ? state.stopSequences.join("\n\n")
        : existingJson.foundation_model.stop_sequence,
      top_k: state.topK || existingJson.foundation_model.top_k,
      top_p: state.topP || existingJson.foundation_model.top_p
    },
    conversation_tone: {
      ...existingJson.conversation_tone,
      message_tone:
        state.defaultTone.charAt(0).toUpperCase() +
          state.defaultTone.slice(1) ||
        existingJson.conversation_tone.message_tone
    },
    retrievers,
    default_retriever: state.retriever || existingJson.default_retriever,
    system_prompt: state.prompt || existingJson.system_prompt,
    feature_toggles: {
      ...existingJson.feature_toggles,
      search_rerank: {
        ...existingJson.feature_toggles.search_rerank,
        config_value:
          state.searchReRank ||
          existingJson.feature_toggles.search_rerank.config_value
      },
      multi_query_enabled: {
        ...existingJson.feature_toggles.multi_query_enabled,
        config_value: state.multiQuery ? "true" : "false"
      }
    },
    guardrails_provider: {
      ...existingJson.guardrails_provider,
      feature_enabled:
        state.guardRail || existingJson.guardrails_provider.feature_enabled
    }
  };
};
