{"name": "@wmx-advisor-desktop/advisor-desktop", "description": "Advisor <PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "clean": "rimraf .turbo node_modules .next out build"}, "dependencies": {"@headlessui/react": "^1.7.7", "@hookform/resolvers": "^3.1.1", "@tanstack/react-query": "^4.29.19", "class-variance-authority": "^0.6.1", "clsx": "^1.2.1", "dompurify": "^3.0.5", "nanoid": "^4.0.2", "next": "^13.4.1", "next-auth": "^4.22.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.1", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "rehype-highlight": "^6.0.0", "rehype-katex": "^6.0.3", "rehype-raw": "^6.1.1", "remark-math": "^5.1.1", "tailwind-merge": "^1.13.2", "wmx-ui": "*"}, "devDependencies": {"@hookform/devtools": "^4.3.1", "@tailwindcss/forms": "^0.5.3", "@testing-library/dom": "^8.20.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/dompurify": "^3.0.2", "@types/jest": "^28.1.8", "@types/node": "^18.11.18", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "autoprefixer": "^10.4.13", "dotenv-cli": "^7.2.1", "eslint-config-custom": "*", "tailwind-config": "*", "tsconfig": "*", "typescript": "5.2.2"}}